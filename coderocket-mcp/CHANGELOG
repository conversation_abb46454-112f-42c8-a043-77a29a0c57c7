# CodeRocket MCP Release Notes

## 发布说明模板

### 版本 1.0.0 - 首次发布

🎉 **CodeRocket MCP 正式发布！**

CodeRocket MCP 是一个基于 Model Context Protocol (MCP) 的智能代码审查服务器，为AI编程工具提供专业的代码审查能力。

#### 🚀 核心功能

- **多维度代码审查**: 支持代码片段、Git提交、文件列表的全面审查
- **多AI服务支持**: 集成Gemini、OpenCode、ClaudeCode等多种AI服务
- **智能故障转移**: 自动切换AI服务，确保审查的可靠性
- **灵活配置管理**: 支持项目级和全局级配置
- **详细错误处理**: 提供用户友好的错误信息和解决建议

#### 📦 安装方式

```bash
# 安装核心依赖
npm install -g coderocket-cli

# 安装CodeRocket MCP
npm install -g @yeepay/coderocket-mcp

# 验证安装
npx -y @yeepay/coderocket-mcp --version
```

#### 🔧 快速配置

在Claude Desktop中配置：

```json
{
  "mcpServers": {
    "coderocket": {
      "command": "npx",
      "args": ["-y", "@yeepay/coderocket-mcp", "start"],
      "env": {
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```

#### 🛠 可用工具

1. **review_code** - 审查代码片段
2. **review_commit** - 审查Git提交
3. **review_files** - 审查文件列表
4. **configure_ai_service** - 配置AI服务
5. **get_ai_service_status** - 获取服务状态

#### 📚 文档和支持

- [完整文档](https://github.com/im47cn/coderocket-cli/tree/main/coderocket-mcp)
- [使用示例](https://github.com/im47cn/coderocket-cli/tree/main/coderocket-mcp/examples)
- [问题反馈](https://github.com/im47cn/coderocket-cli/issues)

#### 🤝 贡献

欢迎社区贡献！请查看我们的[贡献指南](https://github.com/im47cn/coderocket-cli/blob/main/CONTRIBUTING.md)。

---

### 后续版本发布说明

#### 版本 X.Y.Z - 发布日期

**新功能**
- [ ] 功能描述

**改进**
- [ ] 改进描述

**修复**
- [ ] 修复描述

**破坏性变更**
- [ ] 变更描述

**升级指南**
- [ ] 升级步骤

---

## 发布检查清单

发布前请确保：

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] CHANGELOG.md已更新
- [ ] 版本号已正确设置
- [ ] 发布说明已准备
- [ ] npm包配置正确
- [ ] 依赖关系已验证
- [ ] 示例代码已测试
- [ ] 安装脚本已验证

## 发布后任务

- [ ] 更新GitHub Release
- [ ] 通知用户社区
- [ ] 更新相关文档
- [ ] 监控npm下载统计
- [ ] 收集用户反馈
