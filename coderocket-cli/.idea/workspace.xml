<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="434a3ac9-8513-4174-abeb-1bc4e221ae43" name="更改" comment="feat: 添加 VS Code Git Hooks 测试指南和更新 post-commit 脚本">
      <change beforePath="$PROJECT_DIR$/.ai-config" beforeDir="false" afterPath="$PROJECT_DIR$/.ai-config" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;baitao.ji&quot;,
      &quot;fullname&quot;: &quot;baitao.ji&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;*********************:awesome/coderocket-cli.git&quot;,
    &quot;second&quot;: &quot;de01e0d2-6b42-4a5a-b1bf-d2bde0cc0f6c&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zrTBATlF8i121LOrxAwKuGI3ad" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "git-widget-placeholder": "develop",
    "go.import.settings.migrated": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/sources/coderocket/coderocket-cli",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.augmentcode.intellij.settings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/docs" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-bf35d07a577b-intellij.indexing.shared.core-IU-252.23892.360" />
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-IU-252.23892.360" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="434a3ac9-8513-4174-abeb-1bc4e221ae43" name="更改" comment="" />
      <created>1752488054065</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752488054065</updated>
      <workItem from="1752488055528" duration="383000" />
      <workItem from="1752496412407" duration="1467000" />
      <workItem from="1752498010132" duration="210000" />
      <workItem from="1752566226405" duration="104000" />
      <workItem from="1752649181276" duration="634000" />
      <workItem from="1752660099789" duration="153000" />
      <workItem from="1752660710544" duration="41000" />
      <workItem from="1752665016813" duration="70000" />
      <workItem from="1753959521845" duration="307000" />
      <workItem from="1753963637329" duration="389000" />
      <workItem from="1753970789554" duration="90000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 添加 VS Code Git Hooks 测试指南和更新 post-commit 脚本">
      <option name="closed" value="true" />
      <created>1752497347229</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752497347229</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: 添加 VS Code Git Hooks 测试指南和更新 post-commit 脚本" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 添加 VS Code Git Hooks 测试指南和更新 post-commit 脚本" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>