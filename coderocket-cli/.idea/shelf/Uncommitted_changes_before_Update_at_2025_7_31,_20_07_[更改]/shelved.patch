Index: lib/ai-service-manager.sh
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>#!/bin/bash\n\n# AI Service Manager - 多AI服务抽象层\n# 支持 Gemini、OpenCode、ClaudeCode 等多种AI服务\n\n# 颜色定义\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# 默认配置\nDEFAULT_AI_SERVICE=\"gemini\"\nDEFAULT_TIMEOUT=30\n\n# 导入服务模块\nSCRIPT_DIR=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")\" && pwd)\"\nsource \"$SCRIPT_DIR/ai-config.sh\"\nsource \"$SCRIPT_DIR/opencode-service.sh\"\nsource \"$SCRIPT_DIR/claudecode-service.sh\"\n\n# 获取AI服务配置\n#\n# 功能: 按优先级获取当前配置的AI服务\n# 参数: 无\n# 返回: AI服务名称 (gemini/opencode/claudecode)\n# 复杂度: O(1) - 常数时间查找\n# 依赖: grep, cut命令\n# 调用者: smart_ai_call(), show_ai_service_status(), main()\n# 优先级: 环境变量 > 项目配置 > 全局配置 > 默认值\n# 示例:\n#   service=$(get_ai_service)  # 返回 \"gemini\"\nget_ai_service() {\n    # 优先级：环境变量 > 项目配置 > 全局配置 > 默认值\n    local service=\"\"\n\n    # 1. 检查环境变量 (最高优先级)\n    if [ ! -z \"$AI_SERVICE\" ]; then\n        service=\"$AI_SERVICE\"\n    # 2. 检查项目配置文件\n    elif [ -f \".ai-config\" ]; then\n        service=$(grep \"^AI_SERVICE=\" .ai-config 2>/dev/null | cut -d'=' -f2)\n    # 3. 检查全局配置文件\n    elif [ -f \"$HOME/.coderocket/ai-config\" ]; then\n        service=$(grep \"^AI_SERVICE=\" \"$HOME/.coderocket/ai-config\" 2>/dev/null | cut -d'=' -f2)\n    fi\n\n    # 4. 使用默认值 (最低优先级)\n    if [ -z \"$service\" ]; then\n        service=\"$DEFAULT_AI_SERVICE\"\n    fi\n\n    echo \"$service\"\n}\n\n# 检查AI服务是否可用\n#\n# 功能: 检查指定AI服务的CLI工具是否已安装\n# 参数:\n#   $1 - service: AI服务名称 (必需)\n#        支持: \"gemini\", \"opencode\", \"claudecode\"\n# 返回: 0=服务可用, 1=服务不可用或不支持\n# 复杂度: O(1) - 常数时间命令检查\n# 依赖: command命令\n# 调用者: smart_ai_call(), show_ai_service_status()\n# 检查方式: 使用command -v检查CLI工具是否在PATH中\n# 示例:\n#   if check_ai_service_available \"gemini\"; then\n#       echo \"Gemini可用\"\n#   fi\ncheck_ai_service_available() {\n    local service=$1\n\n    case \"$service\" in\n        \"gemini\")\n            command -v gemini &> /dev/null  # 检查gemini命令是否存在\n            ;;\n        \"opencode\")\n            command -v opencode &> /dev/null  # 检查opencode命令是否存在\n            ;;\n        \"claudecode\")\n            command -v claudecode &> /dev/null  # 检查claudecode命令是否存在\n            ;;\n        *)\n            echo -e \"${RED}❌ 不支持的AI服务: $service${NC}\" >&2\n            return 1\n            ;;\n    esac\n}\n\n# 获取AI服务安装命令\n#\n# 功能: 获取指定AI服务的安装命令字符串\n# 参数:\n#   $1 - service: AI服务名称 (必需)\n#        支持: \"gemini\", \"opencode\", \"claudecode\"\n# 返回: 安装命令字符串，未知服务返回\"未知服务\"\n# 复杂度: O(1) - 常数时间查找\n# 依赖: 无\n# 调用者: show_ai_service_status()\n# 用途: 为用户提供安装指导\n# 示例:\n#   cmd=$(get_install_command \"gemini\")\n#   echo \"安装命令: $cmd\"\nget_install_command() {\n    local service=$1\n\n    case \"$service\" in\n        \"gemini\")\n            echo \"npm install -g @google/gemini-cli\"  # Google Gemini CLI\n            ;;\n        \"opencode\")\n            echo \"npm install -g @opencode/cli\"  # OpenCode CLI\n            ;;\n        \"claudecode\")\n            echo \"npm install -g @anthropic-ai/claude-code\"  # ClaudeCode CLI\n            ;;\n        *)\n            echo \"未知服务\"  # 不支持的服务\n            ;;\n    esac\n}\n\n# 获取AI服务配置命令\n#\n# 功能: 获取指定AI服务的配置命令字符串\n# 参数:\n#   $1 - service: AI服务名称 (必需)\n#        支持: \"gemini\", \"opencode\", \"claudecode\"\n# 返回: 配置命令字符串，未知服务返回\"未知服务\"\n# 复杂度: O(1) - 常数时间查找\n# 依赖: 无\n# 调用者: 安装脚本和用户指导\n# 用途: 为用户提供配置指导\n# 示例:\n#   cmd=$(get_config_command \"gemini\")\n#   echo \"配置命令: $cmd\"\nget_config_command() {\n    local service=$1\n\n    case \"$service\" in\n        \"gemini\")\n            echo \"gemini config\"  # Gemini配置命令\n            ;;\n        \"opencode\")\n            echo \"opencode config\"  # OpenCode配置命令\n            ;;\n        \"claudecode\")\n            echo \"claudecode config\"  # ClaudeCode配置命令\n            ;;\n        *)\n            echo \"未知服务\"  # 不支持的服务\n            ;;\n    esac\n}\n\n# 调用AI服务进行代码审查\n#\n# 功能: 使用指定AI服务进行代码审查\n# 参数:\n#   $1 - service: AI服务名称 (必需)\n#   $2 - prompt_file: 提示词文件路径 (必需)\n#   $3 - additional_prompt: 附加提示词 (必需)\n# 返回: 0=成功, 1=文件不存在或服务不支持\n# 复杂度: O(n) - n为提示词文件大小\n# 依赖: cat, gemini命令, opencode_code_review(), claudecode_code_review()\n# 调用者: Git hooks (post-commit)\n# 流程: 验证文件 -> 根据服务类型调用相应函数\n# 示例:\n#   call_ai_for_review \"gemini\" \"prompt.md\" \"请审查代码\"\ncall_ai_for_review() {\n    local service=$1\n    local prompt_file=$2\n    local additional_prompt=$3\n\n    # 验证提示词文件是否存在\n    if [ ! -f \"$prompt_file\" ]; then\n        echo -e \"${RED}❌ 提示词文件不存在: $prompt_file${NC}\" >&2\n        return 1\n    fi\n\n    case \"$service\" in\n        \"gemini\")\n            # 使用管道将文件内容传递给gemini CLI\n            cat \"$prompt_file\" | gemini -p \"$additional_prompt\" -y\n            ;;\n        \"opencode\")\n            # 调用OpenCode服务的代码审查函数\n            opencode_code_review \"$prompt_file\" \"$additional_prompt\"\n            ;;\n        \"claudecode\")\n            # 调用ClaudeCode服务的代码审查函数\n            claudecode_code_review \"$prompt_file\" \"$additional_prompt\"\n            ;;\n        *)\n            echo -e \"${RED}❌ 不支持的AI服务: $service${NC}\" >&2\n            return 1\n            ;;\n    esac\n}\n\n# 调用AI服务生成文本\n#\n# 功能: 使用指定AI服务生成文本内容\n# 参数:\n#   $1 - service: AI服务名称 (必需)\n#   $2 - prompt: 提示词内容 (必需)\n#   $3 - timeout: 超时时间，秒 (可选, 默认: DEFAULT_TIMEOUT)\n# 返回: 0=成功, 1=服务不支持\n# 输出: 生成的文本内容到stdout\n# 复杂度: O(n) - n为提示词长度，实际受AI服务响应时间影响\n# 依赖: echo, timeout, gemini命令, call_opencode_api(), call_claudecode_api()\n# 调用者: smart_ai_call()\n# 超时处理: 使用timeout命令防止长时间等待\n# 示例:\n#   result=$(call_ai_for_generation \"gemini\" \"生成标题\" 30)\ncall_ai_for_generation() {\n    local service=$1\n    local prompt=$2\n    local timeout=${3:-$DEFAULT_TIMEOUT}\n\n    case \"$service\" in\n        \"gemini\")\n            # 使用timeout防止长时间等待，重定向错误输出\n            echo \"$prompt\" | timeout \"$timeout\" gemini -y 2>/dev/null\n            ;;\n        \"opencode\")\n            # 调用OpenCode API函数\n            call_opencode_api \"$prompt\" \"$timeout\"\n            ;;\n        \"claudecode\")\n            # 调用ClaudeCode API函数\n            call_claudecode_api \"$prompt\" \"$timeout\"\n            ;;\n        *)\n            echo -e \"${RED}❌ 不支持的AI服务: $service${NC}\" >&2\n            return 1\n            ;;\n    esac\n}\n\n# 生成备用响应\n#\n# 功能: 当AI服务不可用时生成备用响应内容\n# 参数:\n#   $1 - type: 响应类型 (必需)\n#        支持: \"mr_title\", \"mr_description\"\n#   $2 - context: 上下文信息 (必需)\n#        - mr_title: 分支名称\n#        - mr_description: 提交数量\n# 返回: 无 (直接输出到stdout)\n# 复杂度: O(1) - 常数时间模板生成\n# 依赖: echo命令, 正则表达式匹配\n# 调用者: smart_ai_call()\n# 模式匹配: 使用bash正则表达式识别分支类型\n# 示例:\n#   generate_fallback_response \"mr_title\" \"feature/new-ui\"\n#   generate_fallback_response \"mr_description\" \"5\"\ngenerate_fallback_response() {\n    local type=$1\n    local context=$2\n\n    case \"$type\" in\n        \"mr_title\")\n            local branch_name=$context\n            # 根据分支命名规范生成相应的标题\n            if [[ $branch_name =~ ^feature/.* ]]; then\n                echo \"✨ Feature: ${branch_name#feature/}\"  # 移除feature/前缀\n            elif [[ $branch_name =~ ^fix/.* ]]; then\n                echo \"\uD83D\uDC1B Fix: ${branch_name#fix/}\"  # 移除fix/前缀\n            elif [[ $branch_name =~ ^hotfix/.* ]]; then\n                echo \"\uD83D\uDE91 Hotfix: ${branch_name#hotfix/}\"  # 移除hotfix/前缀\n            else\n                echo \"\uD83D\uDD00 Update: $branch_name\"  # 通用更新\n            fi\n            ;;\n        \"mr_description\")\n            local commit_count=$context\n            # 生成标准的MR描述模板\n            echo \"## \uD83D\uDCCB 变更概述\n\n本次合并包含 **$commit_count** 个提交。\n\n## ✅ 检查清单\n\n- [ ] 代码已经过自测\n- [ ] 相关文档已更新\n- [ ] 测试用例已添加/更新\n- [ ] 无明显的性能影响\n- [ ] 符合代码规范\"\n            ;;\n        *)\n            echo \"AI服务不可用，使用备用方案\"  # 默认备用消息\n            ;;\n    esac\n}\n\n# 智能调用AI服务（带备用方案）\n#\n# 功能: 智能调用AI服务，失败时自动使用备用方案\n# 参数:\n#   $1 - service: AI服务名称 (必需)\n#   $2 - type: 响应类型 (必需) - 用于备用方案\n#   $3 - prompt: 提示词内容 (必需)\n#   $4 - fallback_context: 备用方案上下文 (必需)\n# 返回: 0=总是成功 (AI成功或备用方案)\n# 输出: AI生成的内容或备用方案内容到stdout\n# 复杂度: O(n) - n为提示词长度，受AI服务响应时间影响\n# 依赖: check_ai_service_available(), call_ai_for_generation(), generate_fallback_response()\n# 调用者: Git hooks (pre-push)\n# 容错机制: 服务不可用 -> 备用方案, 调用失败 -> 备用方案, 结果为空 -> 备用方案\n# 示例:\n#   result=$(smart_ai_call \"gemini\" \"mr_title\" \"生成标题\" \"feature/ui\")\nsmart_ai_call() {\n    local service=$1\n    local type=$2\n    local prompt=$3\n    local fallback_context=$4\n\n    # 检查服务是否可用\n    if ! check_ai_service_available \"$service\"; then\n        echo -e \"${YELLOW}⚠ AI服务 $service 不可用，使用备用方案${NC}\" >&2\n        generate_fallback_response \"$type\" \"$fallback_context\"\n        return 0\n    fi\n\n    # 尝试调用AI服务\n    local result=$(call_ai_for_generation \"$service\" \"$prompt\")\n    local exit_code=$?\n\n    # 检查调用是否成功 (退出码非0或结果为空)\n    if [ $exit_code -ne 0 ] || [ -z \"$result\" ]; then\n        echo -e \"${YELLOW}⚠ AI服务调用失败，使用备用方案${NC}\" >&2\n        generate_fallback_response \"$type\" \"$fallback_context\"\n        return 0\n    fi\n\n    # 返回AI生成的结果\n    echo \"$result\"\n}\n\n# 显示AI服务状态\n#\n# 功能: 显示当前AI服务配置和所有服务的安装状态\n# 参数: 无\n# 返回: 无\n# 复杂度: O(n) - n为支持的服务数量\n# 依赖: get_ai_service(), check_ai_service_available(), get_install_command()\n# 调用者: main()\n# 输出格式: 彩色状态报告，包含当前服务和安装状态\n# 示例:\n#   show_ai_service_status\nshow_ai_service_status() {\n    local current_service=$(get_ai_service)\n\n    echo -e \"${BLUE}=== AI服务状态 ===${NC}\"\n    echo \"当前服务: $current_service\"\n    echo \"\"\n\n    # 检查各个服务的可用性\n    local services=(\"gemini\" \"opencode\" \"claudecode\")\n    for service in \"${services[@]}\"; do\n        if check_ai_service_available \"$service\"; then\n            echo -e \"  ${GREEN}✓ $service${NC} - 已安装\"\n        else\n            echo -e \"  ${RED}✗ $service${NC} - 未安装\"\n            echo -e \"    安装命令: $(get_install_command \"$service\")\"\n        fi\n    done\n}\n\n# 设置AI服务\n#\n# 功能: 设置当前使用的AI服务\n# 参数:\n#   $1 - service: AI服务名称 (必需)\n#        支持: \"gemini\", \"opencode\", \"claudecode\"\n#   $2 - scope: 配置范围 (可选, 默认: \"project\")\n#        - \"project\": 保存到项目配置文件\n#        - \"global\": 保存到全局配置文件\n# 返回: 0=设置成功, 1=不支持的服务\n# 复杂度: O(1) - 常数时间文件写入\n# 依赖: mkdir, echo命令\n# 调用者: main()\n# 配置文件: 项目级(.ai-config) 或 全局级(~/.coderocket/ai-config)\n# 示例:\n#   set_ai_service \"gemini\" \"project\"\n#   set_ai_service \"opencode\" \"global\"\nset_ai_service() {\n    local service=$1\n    local scope=${2:-\"project\"}  # project 或 global\n\n    # 验证服务名称\n    case \"$service\" in\n        \"gemini\"|\"opencode\"|\"claudecode\")\n            ;;\n        *)\n            echo -e \"${RED}❌ 不支持的AI服务: $service${NC}\"\n            echo \"支持的服务: gemini, opencode, claudecode\"\n            return 1\n            ;;\n    esac\n\n    # 设置配置\n    if [ \"$scope\" = \"global\" ]; then\n        # 全局配置：创建目录并写入配置文件\n        mkdir -p \"$HOME/.coderocket\"\n        echo \"AI_SERVICE=$service\" > \"$HOME/.coderocket/ai-config\"\n        echo -e \"${GREEN}✓ 全局AI服务设置为: $service${NC}\"\n    else\n        # 项目配置：写入当前目录的配置文件\n        echo \"AI_SERVICE=$service\" > \".ai-config\"\n        echo -e \"${GREEN}✓ 项目AI服务设置为: $service${NC}\"\n    fi\n}\n\n# 主函数 - 用于测试\n#\n# 功能: 命令行接口，提供AI服务管理的各种操作\n# 参数: $@ - 命令行参数\n#   命令格式:\n#     status                        - 显示AI服务状态\n#     set <service> [global|project] - 设置AI服务\n#     test                          - 测试当前AI服务\n# 返回: 0=成功, 1=参数错误\n# 复杂度: O(1) - 命令分发\n# 依赖: show_ai_service_status(), set_ai_service(), get_ai_service(), smart_ai_call()\n# 调用者: 脚本直接执行时\n# 默认命令: status (当无参数时)\n# 示例:\n#   main status\n#   main set gemini global\n#   main test\nmain() {\n    case \"${1:-status}\" in\n        \"status\")\n            show_ai_service_status\n            ;;\n        \"set\")\n            set_ai_service \"$2\" \"$3\"\n            ;;\n        \"test\")\n            local service=$(get_ai_service)\n            echo \"测试AI服务: $service\"\n            # 使用智能调用测试AI服务功能\n            smart_ai_call \"$service\" \"mr_title\" \"生成一个测试标题\" \"test-branch\"\n            ;;\n        *)\n            echo \"用法: $0 {status|set|test}\"\n            echo \"  status - 显示AI服务状态\"\n            echo \"  set <service> [global|project] - 设置AI服务\"\n            echo \"  test - 测试当前AI服务\"\n            ;;\n    esac\n}\n\n# 如果直接执行此脚本，运行主函数\nif [ \"${BASH_SOURCE[0]}\" = \"${0}\" ]; then\n    main \"$@\"\nfi\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/lib/ai-service-manager.sh b/lib/ai-service-manager.sh
--- a/lib/ai-service-manager.sh	(revision 069fb9953a6153b192699d2e07ccd7e8745aa994)
+++ b/lib/ai-service-manager.sh	(date 1753963604299)
@@ -19,6 +19,12 @@
 source "$SCRIPT_DIR/ai-config.sh"
 source "$SCRIPT_DIR/opencode-service.sh"
 source "$SCRIPT_DIR/claudecode-service.sh"
+source "$SCRIPT_DIR/ai-error-classifier.sh" 2>/dev/null
+
+# 智能切换配置
+AI_AUTO_SWITCH=${AI_AUTO_SWITCH:-"true"}
+AI_MAX_RETRIES=${AI_MAX_RETRIES:-3}
+AI_RETRY_DELAY=${AI_RETRY_DELAY:-1}
 
 # 获取AI服务配置
 #
@@ -199,6 +205,133 @@
             ;;
     esac
 }
+
+# 智能代码审查调用函数（多服务自动切换）
+#
+# 功能: 智能调用AI服务进行代码审查，支持自动错误处理和服务切换
+# 参数:
+#   $1 - service: 主要AI服务名称 (必需)
+#   $2 - prompt_file: 提示词文件路径 (必需)
+#   $3 - additional_prompt: 附加提示词 (必需)
+# 返回: 0=成功, 1=失败
+# 特性: 自动错误分类、智能服务切换、用户友好提示
+intelligent_ai_review() {
+    local primary_service=$1
+    local prompt_file=$2
+    local additional_prompt=$3
+
+    # 验证提示词文件是否存在
+    if [ ! -f "$prompt_file" ]; then
+        echo -e "${RED}❌ 提示词文件不存在: $prompt_file${NC}" >&2
+        return 1
+    fi
+
+    # 检查是否启用自动切换
+    if [ "$AI_AUTO_SWITCH" != "true" ]; then
+        # 禁用自动切换，使用原有逻辑
+        return $(call_ai_for_review "$primary_service" "$prompt_file" "$additional_prompt")
+    fi
+
+    # 获取可用服务列表
+    local available_services=($(get_available_services "$primary_service"))
+    local tried_services=()
+
+    echo -e "${BLUE}🔍 开始AI代码审查 (主服务: $primary_service)${NC}" >&2
+
+    # 遍历可用服务
+    for service in "${available_services[@]}"; do
+        # 跳过已尝试的服务
+        if [[ " ${tried_services[@]} " =~ " ${service} " ]]; then
+            continue
+        fi
+
+        tried_services+=("$service")
+
+        echo -e "${YELLOW}→ 使用 $service 进行代码审查...${NC}" >&2
+
+        # 调用代码审查
+        local result=""
+        local exit_code=0
+
+        case "$service" in
+            "gemini")
+                result=$(cat "$prompt_file" | gemini -p "$additional_prompt" -y 2>&1)
+                exit_code=$?
+                ;;
+            "opencode")
+                result=$(opencode_code_review "$prompt_file" "$additional_prompt" 2>&1)
+                exit_code=$?
+                ;;
+            "claudecode")
+                result=$(claudecode_code_review "$prompt_file" "$additional_prompt" 2>&1)
+                exit_code=$?
+                ;;
+            *)
+                echo -e "${RED}❌ 不支持的AI服务: $service${NC}" >&2
+                continue
+                ;;
+        esac
+
+        # 检查调用结果
+        if [ $exit_code -eq 0 ] && [ -n "$result" ]; then
+            echo -e "${GREEN}✅ $service 代码审查完成${NC}" >&2
+            echo "$result"
+            return 0
+        fi
+
+        # 分析错误并决定策略
+        local error_type=$(classify_ai_error "$service" "$exit_code" "$result")
+        local error_desc=$(get_error_description "$error_type" "$service")
+        local strategy=$(get_error_strategy "$error_type")
+
+        echo -e "${YELLOW}$error_desc${NC}" >&2
+
+        # 根据策略处理错误
+        case "$strategy" in
+            "switch_immediately"|"skip_service")
+                echo -e "${YELLOW}→ 切换到下一个服务${NC}" >&2
+                continue
+                ;;
+            "retry_then_switch")
+                echo -e "${YELLOW}→ 重试 $service 服务...${NC}" >&2
+                sleep "$AI_RETRY_DELAY"
+
+                # 重试一次
+                case "$service" in
+                    "gemini")
+                        result=$(cat "$prompt_file" | gemini -p "$additional_prompt" -y 2>&1)
+                        exit_code=$?
+                        ;;
+                    "opencode")
+                        result=$(opencode_code_review "$prompt_file" "$additional_prompt" 2>&1)
+                        exit_code=$?
+                        ;;
+                    "claudecode")
+                        result=$(claudecode_code_review "$prompt_file" "$additional_prompt" 2>&1)
+                        exit_code=$?
+                        ;;
+                esac
+
+                if [ $exit_code -eq 0 ] && [ -n "$result" ]; then
+                    echo -e "${GREEN}✅ $service 代码审查重试成功${NC}" >&2
+                    echo "$result"
+                    return 0
+                else
+                    echo -e "${YELLOW}→ $service 重试失败，切换到下一个服务${NC}" >&2
+                    continue
+                fi
+                ;;
+            *)
+                continue
+                ;;
+        esac
+    done
+
+    # 所有服务都失败
+    echo -e "${RED}❌ 所有AI服务都不可用，代码审查失败${NC}" >&2
+    echo -e "${YELLOW}💡 建议检查AI服务配置或网络连接${NC}" >&2
+    return 1
+}
 
 # 调用AI服务生成文本
 #
@@ -240,6 +373,234 @@
     esac
 }
 
+# 增强版AI调用函数（带详细错误处理）
+#
+# 功能: 调用AI服务并捕获详细错误信息
+# 参数:
+#   $1 - service: AI服务名称 (必需)
+#   $2 - prompt: 提示词内容 (必需)
+#   $3 - timeout: 超时时间，秒 (可选, 默认: DEFAULT_TIMEOUT)
+# 返回: 0=成功, 非0=失败
+# 输出: 成功时输出结果到stdout，失败时错误信息到stderr
+# 全局变量: 设置错误分类器的全局变量
+call_ai_with_error_handling() {
+    local service=$1
+    local prompt=$2
+    local timeout=${3:-$DEFAULT_TIMEOUT}
+
+    # 创建临时文件存储输出和错误
+    local temp_stdout=$(mktemp)
+    local temp_stderr=$(mktemp)
+    local exit_code=0
+
+    # 确保临时文件在函数退出时被清理
+    trap "rm -f '$temp_stdout' '$temp_stderr'" RETURN
+
+    case "$service" in
+        "gemini")
+            # 捕获stdout和stderr
+            echo "$prompt" | timeout "$timeout" gemini -y >"$temp_stdout" 2>"$temp_stderr"
+            exit_code=$?
+            ;;
+        "opencode")
+            # 调用OpenCode API函数，捕获输出
+            call_opencode_api "$prompt" "$timeout" >"$temp_stdout" 2>"$temp_stderr"
+            exit_code=$?
+            ;;
+        "claudecode")
+            # 调用ClaudeCode API函数，捕获输出
+            call_claudecode_api "$prompt" "$timeout" >"$temp_stdout" 2>"$temp_stderr"
+            exit_code=$?
+            ;;
+        *)
+            echo "不支持的AI服务: $service" >"$temp_stderr"
+            exit_code=1
+            ;;
+    esac
+
+    # 读取输出内容
+    local stdout_content=$(cat "$temp_stdout" 2>/dev/null)
+    local stderr_content=$(cat "$temp_stderr" 2>/dev/null)
+
+    # 使用错误分类器分析错误
+    if [ -n "$(command -v classify_ai_error)" ]; then
+        classify_ai_error "$service" "$exit_code" "$stderr_content" "$stdout_content" >/dev/null
+    fi
+
+    # 输出结果
+    if [ $exit_code -eq 0 ] && [ -n "$stdout_content" ]; then
+        echo "$stdout_content"
+        return 0
+    else
+        # 输出错误信息到stderr
+        [ -n "$stderr_content" ] && echo "$stderr_content" >&2
+        return $exit_code
+    fi
+}
+
+# 获取可用AI服务列表
+#
+# 功能: 获取当前可用的AI服务列表，按优先级排序
+# 参数:
+#   $1 - primary_service: 主要服务名称 (可选)
+# 返回: 可用服务列表，空格分隔
+# 优先级: 主要服务 > 其他已安装服务 > 所有支持的服务
+get_available_services() {
+    local primary_service=${1:-$(get_ai_service)}
+    local available_services=()
+    local all_services=("gemini" "opencode" "claudecode")
+
+    # 首先添加主要服务（如果可用）
+    if check_ai_service_available "$primary_service"; then
+        available_services+=("$primary_service")
+    fi
+
+    # 添加其他可用服务
+    for service in "${all_services[@]}"; do
+        if [ "$service" != "$primary_service" ] && check_ai_service_available "$service"; then
+            available_services+=("$service")
+        fi
+    done
+
+    # 如果没有可用服务，返回所有支持的服务（让后续逻辑处理）
+    if [ ${#available_services[@]} -eq 0 ]; then
+        available_services=("${all_services[@]}")
+    fi
+
+    echo "${available_services[@]}"
+}
+
+# 获取服务优先级配置
+#
+# 功能: 从配置文件读取服务优先级设置
+# 返回: 服务优先级列表，空格分隔
+get_service_priority() {
+    local priority_config=""
+
+    # 检查环境变量
+    if [ -n "$AI_SERVICE_PRIORITY" ]; then
+        priority_config="$AI_SERVICE_PRIORITY"
+    # 检查项目配置
+    elif [ -f ".ai-config" ]; then
+        priority_config=$(grep "^AI_SERVICE_PRIORITY=" .ai-config 2>/dev/null | cut -d'=' -f2)
+    # 检查全局配置
+    elif [ -f "$HOME/.coderocket/ai-config" ]; then
+        priority_config=$(grep "^AI_SERVICE_PRIORITY=" "$HOME/.coderocket/ai-config" 2>/dev/null | cut -d'=' -f2)
+    fi
+
+    # 如果有配置，返回配置的优先级
+    if [ -n "$priority_config" ]; then
+        echo "$priority_config"
+    else
+        # 默认优先级
+        echo "gemini opencode claudecode"
+    fi
+}
+
+# 智能AI调用函数（多服务自动切换）
+#
+# 功能: 智能调用AI服务，支持自动错误处理和服务切换
+# 参数:
+#   $1 - service: 主要AI服务名称 (必需)
+#   $2 - type: 响应类型 (必需) - 用于备用方案
+#   $3 - prompt: 提示词内容 (必需)
+#   $4 - fallback_context: 备用方案上下文 (必需)
+# 返回: 0=总是成功 (AI成功或备用方案)
+# 输出: AI生成的内容或备用方案内容到stdout
+# 特性: 自动错误分类、智能服务切换、用户友好提示
+intelligent_ai_call() {
+    local primary_service=$1
+    local type=$2
+    local prompt=$3
+    local fallback_context=$4
+
+    # 检查是否启用自动切换
+    if [ "$AI_AUTO_SWITCH" != "true" ]; then
+        # 禁用自动切换，使用原有逻辑
+        return $(smart_ai_call "$primary_service" "$type" "$prompt" "$fallback_context")
+    fi
+
+    # 获取可用服务列表
+    local available_services=($(get_available_services "$primary_service"))
+    local tried_services=()
+    local success=false
+
+    echo -e "${BLUE}🤖 开始AI服务调用 (主服务: $primary_service)${NC}" >&2
+
+    # 遍历可用服务
+    for service in "${available_services[@]}"; do
+        # 跳过已尝试的服务
+        if [[ " ${tried_services[@]} " =~ " ${service} " ]]; then
+            continue
+        fi
+
+        tried_services+=("$service")
+
+        echo -e "${YELLOW}→ 尝试使用 $service 服务...${NC}" >&2
+
+        # 调用AI服务
+        local result=$(call_ai_with_error_handling "$service" "$prompt")
+        local exit_code=$?
+
+        # 检查调用结果
+        if [ $exit_code -eq 0 ] && [ -n "$result" ]; then
+            echo -e "${GREEN}✅ $service 服务调用成功${NC}" >&2
+            echo "$result"
+            return 0
+        fi
+
+        # 获取错误信息和处理策略
+        local error_type=$(get_last_error_type)
+        local error_desc=$(get_error_description "$error_type" "$service")
+        local strategy=$(get_error_strategy "$error_type")
+
+        echo -e "${YELLOW}$error_desc${NC}" >&2
+
+        # 根据策略处理错误
+        case "$strategy" in
+            "continue")
+                # 成功，不应该到这里
+                echo "$result"
+                return 0
+                ;;
+            "switch_immediately")
+                echo -e "${YELLOW}→ 立即切换到下一个服务${NC}" >&2
+                continue
+                ;;
+            "skip_service")
+                echo -e "${YELLOW}→ 跳过 $service 服务${NC}" >&2
+                continue
+                ;;
+            "retry_then_switch")
+                echo -e "${YELLOW}→ 重试 $service 服务...${NC}" >&2
+                sleep "$AI_RETRY_DELAY"
+
+                # 重试一次
+                result=$(call_ai_with_error_handling "$service" "$prompt")
+                exit_code=$?
+
+                if [ $exit_code -eq 0 ] && [ -n "$result" ]; then
+                    echo -e "${GREEN}✅ $service 服务重试成功${NC}" >&2
+                    echo "$result"
+                    return 0
+                else
+                    echo -e "${YELLOW}→ $service 服务重试失败，切换到下一个服务${NC}" >&2
+                    continue
+                fi
+                ;;
+            *)
+                echo -e "${YELLOW}→ 切换到下一个服务${NC}" >&2
+                continue
+                ;;
+        esac
+    done
+
+    # 所有服务都失败，使用备用方案
+    echo -e "${RED}❌ 所有AI服务都不可用，使用备用方案${NC}" >&2
+    generate_fallback_response "$type" "$fallback_context"
+    return 0
+}
+
 # 生成备用响应
 #
 # 功能: 当AI服务不可用时生成备用响应内容
@@ -318,26 +679,8 @@
     local prompt=$3
     local fallback_context=$4
 
-    # 检查服务是否可用
-    if ! check_ai_service_available "$service"; then
-        echo -e "${YELLOW}⚠ AI服务 $service 不可用，使用备用方案${NC}" >&2
-        generate_fallback_response "$type" "$fallback_context"
-        return 0
-    fi
-
-    # 尝试调用AI服务
-    local result=$(call_ai_for_generation "$service" "$prompt")
-    local exit_code=$?
-
-    # 检查调用是否成功 (退出码非0或结果为空)
-    if [ $exit_code -ne 0 ] || [ -z "$result" ]; then
-        echo -e "${YELLOW}⚠ AI服务调用失败，使用备用方案${NC}" >&2
-        generate_fallback_response "$type" "$fallback_context"
-        return 0
-    fi
-
-    # 返回AI生成的结果
-    echo "$result"
+    # 使用新的智能调用机制（向后兼容）
+    intelligent_ai_call "$service" "$type" "$prompt" "$fallback_context"
 }
 
 # 显示AI服务状态
Index: githooks/post-commit
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>#!/bin/bash\n\n# 获取 Git 仓库根目录\nREPO_ROOT=$(git rev-parse --show-toplevel 2>/dev/null)\n\n# 如果不在 Git 仓库中，退出\nif [ -z \"$REPO_ROOT\" ]; then\n    echo \"❌ 错误：不在 Git 仓库中\"\n    exit 1\nfi\n\n# 安全地加载必要的环境变量\n# 只加载项目相关的环境变量，避免全局profile污染\n\n# 加载项目环境文件\nif [ -f \"$REPO_ROOT/.env\" ]; then\n    # 只加载以特定前缀开头的环境变量，避免污染\n    while IFS='=' read -r key value; do\n        # 跳过注释和空行\n        [[ $key =~ ^[[:space:]]*# ]] && continue\n        [[ -z $key ]] && continue\n\n        # 只加载AI和GitLab相关的环境变量\n        if [[ $key =~ ^(AI_|GITLAB_|GEMINI_|OPENCODE_|CLAUDECODE_) ]]; then\n            export \"$key=$value\"\n        fi\n    done < \"$REPO_ROOT/.env\" 2>/dev/null\nfi\n\n# 加载全局CodeRocket配置\nif [ -f \"$HOME/.coderocket/env\" ]; then\n    while IFS='=' read -r key value; do\n        [[ $key =~ ^[[:space:]]*# ]] && continue\n        [[ -z $key ]] && continue\n\n        if [[ $key =~ ^(AI_|GITLAB_|GEMINI_|OPENCODE_|CLAUDECODE_) ]]; then\n            export \"$key=$value\"\n        fi\n    done < \"$HOME/.coderocket/env\" 2>/dev/null\nfi\n\n# 导入AI服务管理器\nif [ -f \"$REPO_ROOT/lib/ai-service-manager.sh\" ]; then\n    source \"$REPO_ROOT/lib/ai-service-manager.sh\"\nelif [ -f \"$HOME/.coderocket/lib/ai-service-manager.sh\" ]; then\n    source \"$HOME/.coderocket/lib/ai-service-manager.sh\"\nelse\n    echo \"❌ 错误：AI服务管理器未找到\"\n    exit 1\nfi\n\n# 检查提示词文件是否存在\nPROMPT_FILE=\"$REPO_ROOT/prompts/git-commit-review-prompt.md\"\nif [ ! -f \"$PROMPT_FILE\" ]; then\n    echo \"❌ 错误：提示词文件不存在: $PROMPT_FILE\"\n    exit 1\nfi\n\n# 获取当前AI服务\nCURRENT_AI_SERVICE=$(get_ai_service)\n\n# 检查AI服务是否可用\nif ! check_ai_service_available \"$CURRENT_AI_SERVICE\"; then\n    echo \"❌ 错误：AI服务 $CURRENT_AI_SERVICE 不可用\"\n    echo \"安装命令: $(get_install_command \"$CURRENT_AI_SERVICE\")\"\n    exit 1\nfi\n\n# 创建 review_logs 目录（如果不存在）\nmkdir -p \"$REPO_ROOT/review_logs\"\n\necho \"\uD83D\uDE80 正在执行 commit 后的代码审查...\"\necho \"\uD83D\uDCE1 使用AI服务: $CURRENT_AI_SERVICE\"\n\n# 切换到仓库根目录执行\ncd \"$REPO_ROOT\"\n\n# 准备更明确的提示词\nPROMPT=\"请执行以下任务：\n1. 你是代码审查专家，需要对最新的 git commit 进行审查\n2. 使用 git --no-pager show 命令获取最新提交的详细信息\n3. 根据提示词文件中的指导进行全面代码审查\n4. 生成审查报告并保存到 review_logs 目录\n5. 不要询问用户，直接自主执行所有步骤\n6. 这是一个自动化流程，请直接开始执行\"\n\nif call_ai_for_review \"$CURRENT_AI_SERVICE\" \"$PROMPT_FILE\" \"$PROMPT\"; then\n    echo \"\uD83D\uDC4C 代码审查完成\"\n    echo \"\uD83D\uDCDD 审查报告已保存到 $REPO_ROOT/review_logs 目录\"\nelse\n    echo \"❌ 代码审查失败，但不影响提交\"\nfi
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/githooks/post-commit b/githooks/post-commit
--- a/githooks/post-commit	(revision 069fb9953a6153b192699d2e07ccd7e8745aa994)
+++ b/githooks/post-commit	(date 1753963619404)
@@ -84,7 +84,7 @@
 5. 不要询问用户，直接自主执行所有步骤
 6. 这是一个自动化流程，请直接开始执行"
 
-if call_ai_for_review "$CURRENT_AI_SERVICE" "$PROMPT_FILE" "$PROMPT"; then
+if intelligent_ai_review "$CURRENT_AI_SERVICE" "$PROMPT_FILE" "$PROMPT"; then
     echo "👌 代码审查完成"
     echo "📝 审查报告已保存到 $REPO_ROOT/review_logs 目录"
 else
Index: lib/ai-error-classifier.sh
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/lib/ai-error-classifier.sh b/lib/ai-error-classifier.sh
new file mode 100644
--- /dev/null	(date 1753963313751)
+++ b/lib/ai-error-classifier.sh	(date 1753963313751)
@@ -0,0 +1,299 @@
+#!/bin/bash
+
+# AI Error Classifier - 智能AI服务错误分类器
+# 识别和分类不同AI服务的错误类型，为智能切换提供决策依据
+
+# 颜色定义
+RED='\033[0;31m'
+GREEN='\033[0;32m'
+YELLOW='\033[1;33m'
+BLUE='\033[0;34m'
+NC='\033[0m'
+
+# 错误类型常量
+readonly ERROR_SUCCESS="success"
+readonly ERROR_RATE_LIMIT="rate_limit"
+readonly ERROR_AUTH="auth_error"
+readonly ERROR_NETWORK="network_error"
+readonly ERROR_SERVER="server_error"
+readonly ERROR_CLI_MISSING="cli_missing"
+readonly ERROR_TIMEOUT="timeout"
+readonly ERROR_UNKNOWN="unknown_error"
+
+# 全局变量存储最后的错误信息
+LAST_ERROR_TYPE=""
+LAST_ERROR_MESSAGE=""
+LAST_ERROR_SERVICE=""
+
+# 分类AI服务错误
+#
+# 功能: 根据错误输出和退出码分类错误类型
+# 参数:
+#   $1 - service: AI服务名称
+#   $2 - exit_code: 命令退出码
+#   $3 - error_output: 错误输出内容
+#   $4 - stdout_output: 标准输出内容（可选）
+# 返回: 错误类型字符串
+# 全局变量: 设置LAST_ERROR_*变量
+classify_ai_error() {
+    local service=$1
+    local exit_code=$2
+    local error_output=$3
+    local stdout_output=${4:-""}
+    
+    # 重置全局变量
+    LAST_ERROR_TYPE=""
+    LAST_ERROR_MESSAGE="$error_output"
+    LAST_ERROR_SERVICE="$service"
+    
+    # 成功情况
+    if [ $exit_code -eq 0 ] && [ -n "$stdout_output" ]; then
+        LAST_ERROR_TYPE="$ERROR_SUCCESS"
+        echo "$ERROR_SUCCESS"
+        return 0
+    fi
+    
+    # 命令未找到（CLI未安装）
+    if [ $exit_code -eq 127 ] || echo "$error_output" | grep -qi "command not found\|not found\|no such file"; then
+        LAST_ERROR_TYPE="$ERROR_CLI_MISSING"
+        echo "$ERROR_CLI_MISSING"
+        return 0
+    fi
+    
+    # 超时错误
+    if [ $exit_code -eq 124 ] || echo "$error_output" | grep -qi "timeout\|timed out"; then
+        LAST_ERROR_TYPE="$ERROR_TIMEOUT"
+        echo "$ERROR_TIMEOUT"
+        return 0
+    fi
+    
+    # 根据服务类型和错误内容进行分类
+    case "$service" in
+        "gemini")
+            classify_gemini_error "$error_output"
+            ;;
+        "opencode")
+            classify_opencode_error "$error_output"
+            ;;
+        "claudecode")
+            classify_claudecode_error "$error_output"
+            ;;
+        *)
+            classify_generic_error "$error_output"
+            ;;
+    esac
+    
+    echo "$LAST_ERROR_TYPE"
+}
+
+# 分类Gemini CLI错误
+classify_gemini_error() {
+    local error_output=$1
+    
+    # 限流错误 (429, quota exceeded, rate limit)
+    if echo "$error_output" | grep -qi "429\|rate limit\|quota exceeded\|too many requests\|rate_limit_exceeded"; then
+        LAST_ERROR_TYPE="$ERROR_RATE_LIMIT"
+        return 0
+    fi
+    
+    # 认证错误 (401, 403, API key)
+    if echo "$error_output" | grep -qi "401\|403\|unauthorized\|forbidden\|api key\|invalid key\|authentication"; then
+        LAST_ERROR_TYPE="$ERROR_AUTH"
+        return 0
+    fi
+    
+    # 网络错误
+    if echo "$error_output" | grep -qi "network\|connection\|dns\|resolve\|unreachable\|connection refused"; then
+        LAST_ERROR_TYPE="$ERROR_NETWORK"
+        return 0
+    fi
+    
+    # 服务器错误 (5xx)
+    if echo "$error_output" | grep -qi "500\|502\|503\|504\|internal server error\|bad gateway\|service unavailable"; then
+        LAST_ERROR_TYPE="$ERROR_SERVER"
+        return 0
+    fi
+    
+    # 默认为未知错误
+    LAST_ERROR_TYPE="$ERROR_UNKNOWN"
+}
+
+# 分类OpenCode CLI错误
+classify_opencode_error() {
+    local error_output=$1
+    
+    # OpenCode特定的错误模式
+    if echo "$error_output" | grep -qi "rate limit\|quota\|429"; then
+        LAST_ERROR_TYPE="$ERROR_RATE_LIMIT"
+        return 0
+    fi
+    
+    if echo "$error_output" | grep -qi "unauthorized\|invalid token\|authentication failed"; then
+        LAST_ERROR_TYPE="$ERROR_AUTH"
+        return 0
+    fi
+    
+    if echo "$error_output" | grep -qi "connection\|network\|timeout"; then
+        LAST_ERROR_TYPE="$ERROR_NETWORK"
+        return 0
+    fi
+    
+    # 使用通用分类
+    classify_generic_error "$error_output"
+}
+
+# 分类ClaudeCode CLI错误
+classify_claudecode_error() {
+    local error_output=$1
+    
+    # Claude特定的错误模式
+    if echo "$error_output" | grep -qi "rate_limit\|too_many_requests\|429"; then
+        LAST_ERROR_TYPE="$ERROR_RATE_LIMIT"
+        return 0
+    fi
+    
+    if echo "$error_output" | grep -qi "invalid_api_key\|unauthorized\|authentication_error"; then
+        LAST_ERROR_TYPE="$ERROR_AUTH"
+        return 0
+    fi
+    
+    if echo "$error_output" | grep -qi "connection_error\|network\|timeout"; then
+        LAST_ERROR_TYPE="$ERROR_NETWORK"
+        return 0
+    fi
+    
+    # 使用通用分类
+    classify_generic_error "$error_output"
+}
+
+# 通用错误分类
+classify_generic_error() {
+    local error_output=$1
+    
+    # 网络相关错误
+    if echo "$error_output" | grep -qi "network\|connection\|dns\|timeout\|unreachable"; then
+        LAST_ERROR_TYPE="$ERROR_NETWORK"
+        return 0
+    fi
+    
+    # 认证相关错误
+    if echo "$error_output" | grep -qi "auth\|unauthorized\|forbidden\|key\|token"; then
+        LAST_ERROR_TYPE="$ERROR_AUTH"
+        return 0
+    fi
+    
+    # 服务器错误
+    if echo "$error_output" | grep -qi "server error\|internal error\|5[0-9][0-9]"; then
+        LAST_ERROR_TYPE="$ERROR_SERVER"
+        return 0
+    fi
+    
+    # 默认未知错误
+    LAST_ERROR_TYPE="$ERROR_UNKNOWN"
+}
+
+# 获取错误处理策略
+#
+# 功能: 根据错误类型返回处理策略
+# 参数:
+#   $1 - error_type: 错误类型
+# 返回: 处理策略字符串
+get_error_strategy() {
+    local error_type=$1
+    
+    case "$error_type" in
+        "$ERROR_SUCCESS")
+            echo "continue"
+            ;;
+        "$ERROR_RATE_LIMIT")
+            echo "switch_immediately"  # 立即切换到下一个服务
+            ;;
+        "$ERROR_AUTH")
+            echo "skip_service"        # 跳过此服务，提示用户配置
+            ;;
+        "$ERROR_CLI_MISSING")
+            echo "skip_service"        # 跳过此服务，提示安装
+            ;;
+        "$ERROR_NETWORK")
+            echo "retry_then_switch"   # 重试一次，然后切换
+            ;;
+        "$ERROR_TIMEOUT")
+            echo "switch_immediately"  # 立即切换
+            ;;
+        "$ERROR_SERVER")
+            echo "retry_then_switch"   # 重试一次，然后切换
+            ;;
+        *)
+            echo "switch_immediately"  # 未知错误，立即切换
+            ;;
+    esac
+}
+
+# 获取用户友好的错误描述
+get_error_description() {
+    local error_type=$1
+    local service=$2
+    
+    case "$error_type" in
+        "$ERROR_RATE_LIMIT")
+            echo "🚫 $service 服务达到使用限制（429错误）"
+            ;;
+        "$ERROR_AUTH")
+            echo "🔐 $service 服务认证失败，请检查API密钥配置"
+            ;;
+        "$ERROR_CLI_MISSING")
+            echo "📦 $service CLI工具未安装"
+            ;;
+        "$ERROR_NETWORK")
+            echo "🌐 网络连接问题，无法访问 $service 服务"
+            ;;
+        "$ERROR_TIMEOUT")
+            echo "⏰ $service 服务响应超时"
+            ;;
+        "$ERROR_SERVER")
+            echo "🔧 $service 服务器错误"
+            ;;
+        *)
+            echo "❓ $service 服务遇到未知错误"
+            ;;
+    esac
+}
+
+# 获取最后的错误信息
+get_last_error_type() {
+    echo "$LAST_ERROR_TYPE"
+}
+
+get_last_error_message() {
+    echo "$LAST_ERROR_MESSAGE"
+}
+
+get_last_error_service() {
+    echo "$LAST_ERROR_SERVICE"
+}
+
+# 测试函数
+test_error_classifier() {
+    echo "🧪 测试AI错误分类器..."
+    
+    # 测试429错误
+    local result=$(classify_ai_error "gemini" 1 "Error: 429 Too Many Requests - Rate limit exceeded")
+    echo "429错误分类: $result (期望: $ERROR_RATE_LIMIT)"
+    
+    # 测试认证错误
+    result=$(classify_ai_error "gemini" 1 "Error: 401 Unauthorized - Invalid API key")
+    echo "认证错误分类: $result (期望: $ERROR_AUTH)"
+    
+    # 测试网络错误
+    result=$(classify_ai_error "opencode" 1 "Error: Connection timeout")
+    echo "网络错误分类: $result (期望: $ERROR_NETWORK)"
+    
+    # 测试CLI未安装
+    result=$(classify_ai_error "claudecode" 127 "claudecode: command not found")
+    echo "CLI未安装分类: $result (期望: $ERROR_CLI_MISSING)"
+}
+
+# 如果直接运行此脚本，执行测试
+if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
+    test_error_classifier
+fi
Index: githooks/pre-commit
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>#!/bin/bash\n\n# 获取 Git 仓库根目录\nREPO_ROOT=$(git rev-parse --show-toplevel 2>/dev/null)\n\n# 如果不在 Git 仓库中，退出\nif [ -z \"$REPO_ROOT\" ]; then\n    echo \"❌ 错误：不在 Git 仓库中\"\n    exit 1\nfi\n\n# 安全地加载必要的环境变量\n# 只加载项目相关的环境变量，避免全局profile污染\n\n# 加载项目环境文件\nif [ -f \"$REPO_ROOT/.env\" ]; then\n    # 只加载以特定前缀开头的环境变量，避免污染\n    while IFS='=' read -r key value; do\n        # 跳过注释和空行\n        [[ $key =~ ^[[:space:]]*# ]] && continue\n        [[ -z $key ]] && continue\n\n        # 只加载AI和GitLab相关的环境变量\n        if [[ $key =~ ^(AI_|GITLAB_|GEMINI_|OPENCODE_|CLAUDECODE_|REVIEW_) ]]; then\n            export \"$key=$value\"\n        fi\n    done < \"$REPO_ROOT/.env\" 2>/dev/null\nfi\n\n# 加载全局CodeRocket配置\nif [ -f \"$HOME/.coderocket/env\" ]; then\n    while IFS='=' read -r key value; do\n        [[ $key =~ ^[[:space:]]*# ]] && continue\n        [[ -z $key ]] && continue\n\n        if [[ $key =~ ^(AI_|GITLAB_|GEMINI_|OPENCODE_|CLAUDECODE_|REVIEW_) ]]; then\n            export \"$key=$value\"\n        fi\n    done < \"$HOME/.coderocket/env\" 2>/dev/null\nfi\n\n# 导入AI服务管理器\nif [ -f \"$REPO_ROOT/lib/ai-service-manager.sh\" ]; then\n    source \"$REPO_ROOT/lib/ai-service-manager.sh\"\nelif [ -f \"$HOME/.coderocket/lib/ai-service-manager.sh\" ]; then\n    source \"$HOME/.coderocket/lib/ai-service-manager.sh\"\nelse\n    echo \"❌ 错误：AI服务管理器未找到\"\n    exit 1\nfi\n\n# 检查提示词文件是否存在\nPROMPT_FILE=\"$REPO_ROOT/prompts/git-commit-review-prompt.md\"\nif [ ! -f \"$PROMPT_FILE\" ]; then\n    echo \"❌ 错误：提示词文件不存在: $PROMPT_FILE\"\n    exit 1\nfi\n\n# 获取当前AI服务\nCURRENT_AI_SERVICE=$(get_ai_service)\n\n# 检查AI服务是否可用\nif ! check_ai_service_available \"$CURRENT_AI_SERVICE\"; then\n    echo \"❌ 错误：AI服务 $CURRENT_AI_SERVICE 不可用\"\n    echo \"安装命令: $(get_install_command \"$CURRENT_AI_SERVICE\")\"\n    echo \"⚠\uFE0F  跳过代码审查，允许提交继续\"\n    exit 0\nfi\n\n# 创建 review_logs 目录（如果不存在）\nmkdir -p \"$REPO_ROOT/review_logs\"\n\necho \"\uD83D\uDE80 正在执行 commit 前的代码审查...\"\necho \"\uD83D\uDCE1 使用AI服务: $CURRENT_AI_SERVICE\"\n\n# 切换到仓库根目录执行\ncd \"$REPO_ROOT\"\n\n# 获取暂存区的变更内容\nSTAGED_CHANGES=$(git diff --cached)\n\nif [ -z \"$STAGED_CHANGES\" ]; then\n    echo \"⚠\uFE0F  没有暂存的变更，跳过代码审查\"\n    exit 0\nfi\n\n# 准备更明确的提示词\nPROMPT=\"请执行以下任务：\n1. 你是代码审查专家，需要对即将提交的代码变更进行审查\n2. 使用 git diff --cached 命令获取暂存区的变更内容\n3. 根据提示词文件中的指导进行全面代码审查\n4. 重点关注以下方面：\n   - 代码质量和潜在bug\n   - 安全漏洞\n   - 性能问题\n   - 代码规范\n5. 生成审查报告并保存到 review_logs 目录\n6. 如果发现严重问题，在报告中明确标注 [CRITICAL] 或 [BLOCKING]\n7. 不要询问用户，直接自主执行所有步骤\n8. 这是一个提交前自动化流程，请直接开始执行\n\n暂存区变更内容：\n$STAGED_CHANGES\"\n\n# 执行代码审查\nREVIEW_OUTPUT=\"\"\nif REVIEW_OUTPUT=$(call_ai_for_review \"$CURRENT_AI_SERVICE\" \"$PROMPT_FILE\" \"$PROMPT\"); then\n    echo \"\uD83D\uDC4C 代码审查完成\"\n    echo \"\uD83D\uDCDD 审查报告已保存到 $REPO_ROOT/review_logs 目录\"\n    \n    # 检查是否有严重问题\n    if echo \"$REVIEW_OUTPUT\" | grep -q -E \"\\[CRITICAL\\]|\\[BLOCKING\\]\"; then\n        echo \"\"\n        echo \"⚠\uFE0F  发现严重问题！\"\n        echo \"\uD83D\uDCCB 审查摘要：\"\n        echo \"$REVIEW_OUTPUT\" | grep -E \"\\[CRITICAL\\]|\\[BLOCKING\\]\" | head -5\n        echo \"\"\n        \n        # 询问用户是否继续提交\n        read -p \"是否仍要继续提交？(y/N): \" -n 1 -r\n        echo\n        if [[ ! $REPLY =~ ^[Yy]$ ]]; then\n            echo \"❌ 提交已取消\"\n            echo \"\uD83D\uDCA1 请修复上述问题后重新提交\"\n            exit 1\n        else\n            echo \"⚠\uFE0F  用户选择继续提交，尽管存在严重问题\"\n        fi\n    else\n        echo \"✅ 未发现严重问题，允许提交\"\n    fi\nelse\n    echo \"❌ 代码审查失败，但不阻止提交\"\n    echo \"\uD83D\uDCA1 建议检查AI服务配置或网络连接\"\nfi\n\nexit 0\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/githooks/pre-commit b/githooks/pre-commit
--- a/githooks/pre-commit	(revision 069fb9953a6153b192699d2e07ccd7e8745aa994)
+++ b/githooks/pre-commit	(date 1753963630248)
@@ -104,7 +104,7 @@
 
 # 执行代码审查
 REVIEW_OUTPUT=""
-if REVIEW_OUTPUT=$(call_ai_for_review "$CURRENT_AI_SERVICE" "$PROMPT_FILE" "$PROMPT"); then
+if REVIEW_OUTPUT=$(intelligent_ai_review "$CURRENT_AI_SERVICE" "$PROMPT_FILE" "$PROMPT"); then
     echo "👌 代码审查完成"
     echo "📝 审查报告已保存到 $REPO_ROOT/review_logs 目录"
     
Index: .env.example
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># CodeRocket CLI 环境变量配置示例\n# 复制此文件为 .env 并填入实际值\n# 标注说明：[必填] = 必须配置，[选填] = 可选配置\n\n# ==================== GitLab 配置 ====================\n\n# [必填] GitLab Personal Access Token\n# 在 GitLab 中生成：Settings > Access Tokens > Personal Access Tokens\n# 需要的权限：api, read_repository, write_repository\nGITLAB_PERSONAL_ACCESS_TOKEN=your_gitlab_token_here\n\n# [选填] GitLab API URL（默认为 https://gitlab.com/api/v4）\n# 如果使用私有 GitLab 实例，请修改此 URL\nGITLAB_API_URL=https://gitlab.com/api/v4\n\n# ==================== AI 服务配置 ====================\n\n# [选填] 选择使用的AI服务（默认: gemini）\n# 支持的服务：gemini, opencode, claudecode\nAI_SERVICE=gemini\n\n# [选填] 代码审查时机（默认: post-commit）\n# 支持的时机：pre-commit（提交前审查）, post-commit（提交后审查）\nREVIEW_TIMING=post-commit\n\n# [选填] AI服务调用超时时间，单位：秒（默认: 30）\nAI_TIMEOUT=30\n\n# [选填] AI服务重试次数（默认: 3）\nAI_MAX_RETRIES=3\n\n# ==================== Gemini 配置 ====================\n\n# [必填] Gemini API Key（如果使用Gemini服务）\n# 获取地址：https://aistudio.google.com/app/apikey\nGEMINI_API_KEY=your_gemini_api_key_here\n\n# [选填] Gemini 模型（默认: gemini-pro）\nGEMINI_MODEL=gemini-pro\n\n# ==================== OpenCode 配置 ====================\n\n# [必填] OpenCode API Key（如果使用OpenCode服务）\nOPENCODE_API_KEY=your_opencode_api_key_here\n\n# [选填] OpenCode API URL（默认: https://api.opencode.com/v1）\nOPENCODE_API_URL=https://api.opencode.com/v1\n\n# [选填] OpenCode 模型（默认: opencode-pro）\nOPENCODE_MODEL=opencode-pro\n\n# ==================== ClaudeCode 配置 ====================\n\n# [必填] ClaudeCode API Key（如果使用ClaudeCode服务）\nCLAUDECODE_API_KEY=your_claudecode_api_key_here\n\n# [选填] ClaudeCode API URL（默认: https://api.claudecode.com/v1）\nCLAUDECODE_API_URL=https://api.claudecode.com/v1\n\n# [选填] ClaudeCode 模型（默认: claude-3-sonnet）\nCLAUDECODE_MODEL=claude-3-sonnet\n\n# ==================== 其他配置 ====================\n\n# [选填] 审查报告输出目录（默认: ./review_logs）\nREVIEW_LOGS_DIR=./review_logs\n\n# [选填] 启用调试模式（默认: false）\nDEBUG=false\n\n# ==================== 配置示例 ====================\n\n# 示例1：使用Gemini服务\n# AI_SERVICE=gemini\n# GEMINI_API_KEY=your_actual_gemini_key\n# GITLAB_PERSONAL_ACCESS_TOKEN=glpat-xxxxxxxxxxxxxxxxxxxx\n\n# 示例2：使用OpenCode服务\n# AI_SERVICE=opencode\n# OPENCODE_API_KEY=your_actual_opencode_key\n# GITLAB_PERSONAL_ACCESS_TOKEN=glpat-xxxxxxxxxxxxxxxxxxxx\n\n# 示例3：使用私有GitLab实例\n# GITLAB_API_URL=https://gitlab.yeepay.com/api/v4\n# GITLAB_PERSONAL_ACCESS_TOKEN=glpat-xxxxxxxxxxxxxxxxxxxx\n\n# AI 服务配置\n# 选择使用的AI服务：gemini, opencode, claudecode\nAI_SERVICE=gemini\n\n# Gemini 配置\nGEMINI_API_KEY=your_gemini_api_key_here\nGEMINI_MODEL=gemini-pro\n\n# OpenCode 配置\nOPENCODE_API_KEY=your_opencode_api_key_here\nOPENCODE_API_URL=https://api.opencode.com/v1\nOPENCODE_MODEL=opencode-pro\n\n# ClaudeCode 配置\nCLAUDECODE_API_KEY=your_claudecode_api_key_here\nCLAUDECODE_API_URL=https://api.claudecode.com/v1\nCLAUDECODE_MODEL=claude-3-sonnet\n\n# AI 服务通用配置\nAI_TIMEOUT=30\nAI_MAX_RETRIES=3\n\n# 示例：\n# GITLAB_PERSONAL_ACCESS_TOKEN=glpat-xxxxxxxxxxxxxxxxxxxx\n# GITLAB_API_URL=https://gitlab.yeepay.com/api/v4\n# AI_SERVICE=gemini\n# GEMINI_API_KEY=your_actual_gemini_key\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.env.example b/.env.example
--- a/.env.example	(revision 069fb9953a6153b192699d2e07ccd7e8745aa994)
+++ b/.env.example	(date 1753962966098)
@@ -1,4 +1,4 @@
-# CodeRocket CLI 环境变量配置示例
+# CodeReview CLI 环境变量配置示例
 # 复制此文件为 .env 并填入实际值
 # 标注说明：[必填] = 必须配置，[选填] = 可选配置
 
