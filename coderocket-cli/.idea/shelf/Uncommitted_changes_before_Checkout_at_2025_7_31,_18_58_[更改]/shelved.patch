Index: lib/banner.sh
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>#!/bin/bash\n\n# CodeRocket Banner Display\n# 显示项目 banner 和版本信息，参考 Gemini CLI 的精美设计\n\n# 基础颜色定义\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nPURPLE='\\033[0;35m'\nCYAN='\\033[0;36m'\nWHITE='\\033[1;37m'\nBOLD='\\033[1m'\nGRAY='\\033[0;37m'\nNC='\\033[0m' # No Color\n\n# 渐变色定义（模仿 Gemini CLI 的蓝绿渐变，使用 256 色）\nGRAD_1='\\033[38;5;39m'   # 亮蓝色\nGRAD_2='\\033[38;5;45m'   # 青蓝色\nGRAD_3='\\033[38;5;51m'   # 青色\nGRAD_4='\\033[38;5;87m'   # 浅青色\nGRAD_5='\\033[38;5;123m'  # 浅蓝绿色\nGRAD_6='\\033[38;5;159m'  # 很浅的青色\n\n# 获取终端宽度\nget_terminal_width() {\n    tput cols 2>/dev/null || echo 80\n}\n\n# 获取版本信息\nget_version() {\n    if [ -f \"$HOME/.coderocket/VERSION\" ]; then\n        cat \"$HOME/.coderocket/VERSION\" | tr -d '\\n'\n    elif [ -f \"./VERSION\" ]; then\n        cat \"./VERSION\" | tr -d '\\n'\n    else\n        echo \"1.0.4\"\n    fi\n}\n\n# 精美的 CodeRocket ASCII Art（类似 Gemini 的像素风格）\nshow_banner() {\n    local width=$(get_terminal_width)\n\n    # 根据终端宽度选择不同的 banner\n    if [ \"$width\" -ge 100 ]; then\n        show_long_banner\n    else\n        show_short_banner\n    fi\n}\n\n# 长版本 Banner（宽终端使用）\nshow_long_banner() {\n    echo \"\"\n    echo -e \"${GRAD_1} ███            ██████  ██████  ██████  ███████ ██████   ██████   ██████ ██   ██ ███████ ████████ ${NC}\"\n    echo -e \"${GRAD_2}░░░███         ██      ██    ██ ██   ██ ██      ██   ██ ██    ██ ██      ██  ██  ██         ██    ${NC}\"\n    echo -e \"${GRAD_3}  ░░░███       ██      ██    ██ ██   ██ █████   ██████  ██    ██ ██      █████   █████      ██    ${NC}\"\n    echo -e \"${GRAD_4}    ░░░███     ██      ██    ██ ██   ██ ██      ██   ██ ██    ██ ██      ██  ██  ██         ██    ${NC}\"\n    echo -e \"${GRAD_5}     ███░       ██████  ██████  ██████  ███████ ██   ██  ██████   ██████ ██   ██ ███████    ██    ${NC}\"\n    echo -e \"${GRAD_6}   ███░                                                                                            ${NC}\"\n    echo -e \"${GRAD_1} ███░                                                                                              ${NC}\"\n    echo -e \"${GRAD_2}░░░                                                                                                ${NC}\"\n    echo \"\"\n\n    # 版本和兼容性信息\n    local version=$(get_version)\n    echo -e \"${GRAD_5}\uD83D\uDE80 AI 驱动的代码审查工具${NC}\"\n    echo -e \"${GRAY}版本: ${version}${NC}\"\n    echo -e \"${GRAY}兼容命令: coderocket, codereview-cli, cr${NC}\"\n    echo \"\"\n}\n\n# 短版本 Banner（窄终端使用）\nshow_short_banner() {\n    echo \"\"\n    echo -e \"${GRAD_1} ██████  ██████  ██████  ███████ ██████   ██████   ██████ ██   ██ ███████ ████████ ${NC}\"\n    echo -e \"${GRAD_2}██      ██    ██ ██   ██ ██      ██   ██ ██    ██ ██      ██  ██  ██         ██    ${NC}\"\n    echo -e \"${GRAD_3}██      ██    ██ ██   ██ █████   ██████  ██    ██ ██      █████   █████      ██    ${NC}\"\n    echo -e \"${GRAD_4}██      ██    ██ ██   ██ ██      ██   ██ ██    ██ ██      ██  ██  ██         ██    ${NC}\"\n    echo -e \"${GRAD_5} ██████  ██████  ██████  ███████ ██   ██  ██████   ██████ ██   ██ ███████    ██    ${NC}\"\n    echo \"\"\n\n    # 版本和兼容性信息\n    local version=$(get_version)\n    echo -e \"${GRAD_5}\uD83D\uDE80 AI 驱动的代码审查工具${NC}\"\n    echo -e \"${GRAY}版本: ${version}${NC}\"\n    echo -e \"${GRAY}兼容命令: coderocket, codereview-cli, cr${NC}\"\n    echo \"\"\n}\n\n# 迷你 Banner（单行显示）\nshow_mini_banner() {\n    echo -e \"${GRAD_3}CodeRocket \uD83D\uDE80 - AI 驱动的代码审查工具${NC}\"\n}\n\n# 启动信息显示\nshow_startup_info() {\n    show_banner\n    echo -e \"${YELLOW}\uD83D\uDCA1 提示：${NC}\"\n    echo -e \"${WHITE}  • 在 Git 仓库中运行可直接进行代码审查${NC}\"\n    echo -e \"${WHITE}  • 使用 ${BOLD}coderocket help${NC} 查看所有命令${NC}\"\n    echo -e \"${WHITE}  • 使用 ${BOLD}coderocket config${NC} 配置 AI 服务${NC}\"\n    echo \"\"\n}\n\n# 安装 Banner\nshow_install_banner() {\n    show_banner\n    echo -e \"${GRAD_5}\uD83D\uDE80 一键安装脚本${NC}\"\n    echo \"\"\n}\n\n# 显示错误 banner\nshow_error_banner() {\n    local error_msg=\"$1\"\n    echo -e \"${RED}${BOLD}❌ CodeRocket 错误${NC}\"\n    echo -e \"${RED}${error_msg}${NC}\"\n    echo \"\"\n}\n\n# 显示成功 banner\nshow_success_banner() {\n    local success_msg=\"$1\"\n    echo -e \"${GREEN}${BOLD}✅ CodeRocket${NC}\"\n    echo -e \"${GREEN}${success_msg}${NC}\"\n    echo \"\"\n}\n\n# 如果直接运行此脚本\nif [ \"${BASH_SOURCE[0]}\" == \"${0}\" ]; then\n    case \"${1:-startup}\" in\n        \"banner\")\n            show_banner\n            ;;\n        \"mini\")\n            show_mini_banner\n            ;;\n        \"startup\")\n            show_startup_info\n            ;;\n        \"error\")\n            show_error_banner \"$2\"\n            ;;\n        \"success\")\n            show_success_banner \"$2\"\n            ;;\n        \"install\")\n            show_install_banner\n            ;;\n        *)\n            show_startup_info\n            ;;\n    esac\nfi\n
===================================================================
diff --git a/lib/banner.sh b/lib/banner.sh
--- a/lib/banner.sh	(revision f4c8152fffb1be8c7207b7481cc2ff1f645e46f9)
+++ b/lib/banner.sh	(date 1753956436579)
@@ -35,7 +35,7 @@
     elif [ -f "./VERSION" ]; then
         cat "./VERSION" | tr -d '\n'
     else
-        echo "1.0.4"
+        echo "1.0.5"
     fi
 }
 
Index: VERSION
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>1.0.4\n
===================================================================
diff --git a/VERSION b/VERSION
--- a/VERSION	(revision f4c8152fffb1be8c7207b7481cc2ff1f645e46f9)
+++ b/VERSION	(date 1753956420242)
@@ -1,1 +1,1 @@
-1.0.4
****.5
Index: CHANGELOG.md
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># CodeRocket 更新日志\n\n## [v1.0.4] - 2025-01-31\n\n### \uD83D\uDE80 重大改进：自动配置用户命令和 PATH\n\n#### ✨ 新功能\n- **自动创建用户命令**：安装时自动在 `~/.local/bin/` 创建用户级别的命令\n- **智能 PATH 配置**：自动检测用户 shell 并配置 PATH 环境变量\n- **多 Shell 支持**：支持 bash、zsh、fish 等主流 shell\n- **备选方案保障**：即使全局命令失败，用户命令也能正常工作\n\n#### \uD83D\uDEE0\uFE0F 技术改进\n- 新增 `setup_user_commands()` 函数：创建用户级别的命令别名\n- 新增 `configure_user_path()` 函数：智能配置 PATH 环境变量\n- 增强安装流程：无论选择哪种安装模式，都会设置用户命令作为备选\n- 改进安装完成提示：根据实际可用命令显示使用说明\n\n#### \uD83D\uDCA1 用户体验提升\n- **零配置使用**：安装完成后用户可以直接使用 `cr`、`coderocket`、`codereview-cli` 命令\n- **自动备份**：修改配置文件前自动备份原文件\n- **智能检测**：自动检测当前 PATH 状态并给出相应提示\n- **跨平台兼容**：支持 macOS 和 Linux 的不同 shell 配置文件\n\n#### \uD83D\uDD27 解决的问题\n- 解决了用户安装后需要手动配置 PATH 的问题\n- 解决了全局命令失败时用户无法使用的问题\n- 解决了不同 shell 环境下配置不一致的问题\n\n---\n\n## [v1.0.3] - 2025-01-31\n\n### \uD83D\uDD27 重要修复：全局命令语法错误自动检测和修复\n\n#### \uD83D\uDEA8 问题修复\n- **修复全局命令语法错误**：解决了 `/usr/local/bin/cr` 等命令中的转义字符问题\n- **智能错误检测**：安装脚本现在能自动检测有语法错误的全局命令\n- **自动修复机制**：重新安装时会自动覆盖有问题的全局命令文件\n- **语法验证**：创建命令后会自动验证语法正确性\n\n#### \uD83D\uDEE0\uFE0F 技术改进\n- 增强了 `install.sh` 中的 `create_command` 函数\n- 添加了 `check_and_fix_commands` 函数来检测问题命令\n- 修复了命令模板中的转义字符错误（`OLD_VERSION=\\$(cat` 而不是 `OLD_VERSION=\\\\\\$(cat`）\n- 提供了专门的修复脚本 `fix-global-commands-only.sh`\n\n#### \uD83D\uDCE6 新增工具\n- **fix-global-commands-only.sh**：专门修复全局命令的独立脚本\n- **quick-fix.md**：详细的问题解决方案文档\n- 多种修复方案：自动修复、手动修复、临时解决方案\n\n#### \uD83D\uDCA1 用户指南\n- 运行 `bash fix-global-commands-only.sh` 可快速修复全局命令\n- 重新运行安装脚本会自动检测并修复问题命令\n- 本地命令 `bash bin/coderocket` 始终可用作备选方案\n- **新增**：`create-user-commands.sh` 在用户目录创建无需管理员权限的命令\n\n#### ✅ 验证修复结果\n- 成功在用户目录 `~/.local/bin/` 创建了可执行的命令\n- 所有三个命令别名 (`cr`, `codereview-cli`, `coderocket`) 都正常工作\n- 显示正确的版本信息 (1.0.3) 和精美的渐变 banner\n- 无需管理员权限，完全在用户空间运行\n\n---\n\n## [v1.0.2] - 2025-01-31\n\n### \uD83C\uDFA8 界面优化：Gemini CLI 风格的精美 Banner\n\n#### ✨ 新功能\n- **渐变 Banner 设计**：参考 Gemini CLI 的精美设计风格，使用 256 色渐变效果\n- **响应式界面**：根据终端宽度自动选择合适的 ASCII art 显示\n- **专业级视觉效果**：蓝绿渐变色彩，媲美 Gemini CLI 的高质量界面\n- **多场景 Banner**：支持启动、安装、帮助等不同场景的 banner 显示\n\n#### \uD83D\uDEE0\uFE0F 技术改进\n- 优化 banner 显示逻辑，支持多种终端环境\n- 改进脚本加载机制，确保 banner 在所有命令中正确显示\n- 统一所有命令的视觉风格和用户体验\n- 增强终端兼容性，支持不同宽度的终端窗口\n\n#### \uD83D\uDCAB 用户体验\n- 启动时显示精美的渐变 banner，提升专业感\n- 帮助和版本信息界面更加美观易读\n- 安装脚本界面全面升级，视觉效果更佳\n- 所有交互界面保持一致的设计风格\n\n---\n\n## [v1.0.1] - 2025-01-31\n\n### \uD83C\uDF89 重大更新：项目重命名为 CodeRocket\n\n#### ✨ 新功能\n- **项目重命名**：CodeReview CLI 正式更名为 **CodeRocket**\n- **多命令兼容性**：支持 `coderocket`、`codereview-cli`、`cr` 三个命令别名\n- **精美 Banner**：添加了好看的 ASCII art banner，提升用户体验\n- **智能命令检测**：根据当前使用的命令名称显示相应的帮助信息\n\n#### \uD83D\uDD04 向后兼容\n- 完全兼容原有的 `codereview-cli` 命令\n- 支持简短别名 `cr` 命令\n- 所有功能保持不变，用户无需修改使用习惯\n\n#### \uD83D\uDCDD 文档更新\n- 更新 README.md 文档，反映新的项目名称\n- 更新安装脚本中的所有引用\n- 更新 Git hooks 中的配置路径\n- 更新所有帮助信息和错误提示\n\n#### \uD83D\uDEE0\uFE0F 技术改进\n- 重构全局命令创建逻辑\n- 优化 banner 显示系统\n- 改进命令行界面的用户体验\n- 统一配置文件路径（从 `~/.codereview-cli` 到 `~/.coderocket`）\n\n#### \uD83D\uDCE6 安装更新\n- 安装目录更改为 `~/.coderocket`\n- 仓库地址更新为 `https://github.com/im47cn/coderocket`\n- 保持一键安装脚本的便利性\n\n### \uD83D\uDD27 使用说明\n\n#### 新用户\n```bash\n# 一键安装\ncurl -fsSL https://raw.githubusercontent.com/im47cn/coderocket/main/install.sh | bash\n\n# 使用任意命令\ncoderocket help\ncodereview-cli help  # 完全兼容\ncr help              # 简短别名\n```\n\n#### 老用户\n- 无需任何操作，原有命令继续有效\n- 建议重新运行安装脚本获取最新功能\n- 可以开始使用新的 `coderocket` 命令\n\n---\n\n## [v1.0.0] - 2025-01-30\n\n### \uD83C\uDFAF 初始版本\n- AI 驱动的代码审查功能\n- 支持 Git hooks 自动触发\n- 集成 Google Gemini AI\n- 支持 GitLab MR 自动创建\n- 全局和项目级配置支持\n
===================================================================
diff --git a/CHANGELOG.md b/CHANGELOG.md
--- a/CHANGELOG.md	(revision f4c8152fffb1be8c7207b7481cc2ff1f645e46f9)
+++ b/CHANGELOG.md	(date 1753956412416)
@@ -1,5 +1,30 @@
 # CodeRocket 更新日志
 
+## [v1.0.5] - 2025-01-31
+
+### 🎨 用户体验改进
+- **增强Banner展示**: 在代码审查、项目设置、AI配置等命令中添加了banner展示
+- **更好的视觉反馈**: 用户在使用各种命令时都能看到专业的CodeRocket标识
+
+### 📚 文档优化
+- **更新故障排除指南**: 移除了过时的手动PATH配置说明，添加了"命令找不到"问题的完整解决方案
+- **增强部署指南**: 添加了v1.0.4+版本安装脚本自动化特性的详细说明
+- **改进用户指导**: 提供了更清晰的问题诊断和解决步骤
+
+### 🔧 代码重构
+- **Shell检测逻辑重构**: 将shell检测功能拆分为独立的函数，提高代码可读性和可维护性
+  - `detect_user_shell()`: 检测用户shell类型
+  - `get_shell_config_file()`: 获取shell配置文件路径
+  - `generate_path_config()`: 生成PATH配置语句
+- **删除重复代码**: 移除了install.sh中重复的`configure_user_path()`函数定义
+- **改进代码组织**: 使用模块化函数替代内联逻辑，提高代码复用性
+
+### 🛠️ 技术改进
+- **更好的错误处理**: 增强了安装脚本的错误检测和恢复能力
+- **代码质量提升**: 通过函数化重构提高了代码的可测试性和可维护性
+
+---
+
 ## [v1.0.4] - 2025-01-31
 
 ### 🚀 重大改进：自动配置用户命令和 PATH
